;
; Sample l2tpd configuration file
;
; This example file should give you some idea of how the options for l2tpd
; should work.  The best place to look for a list of all options is in
; the source code itself, until I have the time to write better documentation :)
; Specifically, the file "file.c" contains a list of commands at the end.
;
; You most definitely don't have to spell out everything as it is done here
;
; [global]								; Global parameters:
; port = 1701						 	; * Bind to port 1701
; auth file = /etc/l2tpd/l2tp-secrets 	; * Where our challenge secrets are
; access control = yes					; * Refuse connections without IP match
; rand source = dev                     ; Source for entropy for random
;                                       ; numbers, options are:
;                                       ; dev - reads of /dev/urandom
;                                       ; sys - uses rand()
;                                       ; egd - reads from egd socket
;                                       ; egd is not yet implemented
;
; [lns default]							; Our fallthrough LNS definition
; exclusive = no						; * Only permit one tunnel per host
; ip range = ***********-************	; * Allocate from this IP range
; no ip range = ***********-*********** ; * Except these hosts
; ip range = ***********				; * But this one is okay
; ip range = lac1-lac2					; * And anything from lac1 to lac2's IP
; lac = *********** - ***********		; * These can connect as LAC's
; no lac = untrusted.marko.net			; * This guy can't connect
; hidden bit = no						; * Use hidden AVP's?
; local ip = ***********				; * Our local IP to use
; local ip range = *************-**************   ; Alternatively, use a range for local addressing
; length bit = yes						; * Use length bit in payload?
; require chap = yes					; * Require CHAP auth. by peer
; refuse pap = yes						; * Refuse PAP authentication
; refuse chap = no						; * Refuse CHAP authentication
; refuse authentication = no			; * Refuse authentication altogether
; require authentication = yes			; * Require peer to authenticate
; unix authentication = no				; * Use /etc/passwd for auth.
; name = myhostname						; * Report this as our hostname
; ppp debug = no						; * Turn on PPP debugging
; pppoptfile = /etc/ppp/options.l2tpd.lns	; * ppp options file
; call rws = 10							; * RWS for call (-1 is valid)
; tunnel rws = 4						; * RWS for tunnel (must be > 0)
; flow bit = yes						; * Include sequence numbers
; challenge = yes						; * Challenge authenticate peer ; 
; rx bps = 10000000				; Receive tunnel speed
; tx bps = 10000000				; Transmit tunnel speed
; bps = 100000					; Define both receive and transmit speed in one option

; [lac marko]							; Example VPN LAC definition
; lns = lns.marko.net					; * Who is our LNS?
; lns = lns2.marko.net					; * A backup LNS (not yet used)
; redial = yes							; * Redial if disconnected?
; redial timeout = 15					; * Wait n seconds between redials
; max redials = 5						; * Give up after n consecutive failures
; hidden bit = yes						; * User hidden AVP's?
; local ip = ***********				; * Force peer to use this IP for us
; remote ip = ***********				; * Force peer to use this as their IP
; length bit = no						; * Use length bit in payload?
; require pap = no						; * Require PAP auth. by peer
; require chap = yes					; * Require CHAP auth. by peer
; refuse pap = yes						; * Refuse PAP authentication
; refuse chap = no						; * Refuse CHAP authentication
; refuse authentication = no			; * Refuse authentication altogether
; require authentication = yes			; * Require peer to authenticate
; name = marko							; * Report this as our hostname
; ppp debug = no						; * Turn on PPP debugging
; pppoptfile = /etc/ppp/options.l2tpd.marko	; * ppp options file for this lac
; call rws = 10							; * RWS for call (-1 is valid)
; tunnel rws = 4						; * RWS for tunnel (must be > 0)
; flow bit = yes						; * Include sequence numbers
; challenge = yes						; * Challenge authenticate peer 
;
; [lac cisco]							; Another quick LAC
; lns = cisco.marko.net					; * Required, but can take from default
; require authentication = yes			
