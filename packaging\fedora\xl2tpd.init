#!/bin/sh
#
# xl2tpd	This shell script takes care of starting and stopping l2tpd.
#
# chkconfig: - 80 30
# description:	Layer 2 Tunnelling Protocol Daemon (RFC 2661)
#
# processname:	/usr/sbin/xl2tpd
# config:	/etc/xl2tpd/xl2tpd.conf
# pidfile:	/var/run/xl2tpd.pid

### BEGIN INIT INFO
# Provides: xl2tpd
# Required-Start: $local_fs $network $syslog
# Required-Stop: $local_fs $network $syslog
# Default-Start:
# Default-Stop: 0 1 2 3 4 5 6
# Short-Description: start|stop|status|restart|try-restart|reload|force-reload xl2tpd server
# Description: control xl2tpd server
### END INIT INFO

#Servicename
SERVICE=xl2tpd
PATHTOSERV=/usr/sbin/
# Source function library.
. /etc/rc.d/init.d/functions

# Source networking configuration.
. /etc/sysconfig/network

if [ "${NETWORKING}" = "no" ]
then
	echo -n "No Networking"
	exit 0
fi

if [ ! -x $PATHTOSERV$SERVICE ]
then
	echo -n "No bin $SERVICE found, try to find auto"
	SERVICE=$(find / -name $SERVICE | grep bin)
	if [ ! -x $SERVICE ] 
	then
		echo -n "No bin $SERVICE auto found, please check this"
		exit 0
	fi
else
	SERVICE=$PATHTOSERV$SERVICE
fi

RETVAL=0

start() {
	echo -n "Starting $SERVICE: "
	if [ ! -d /var/run/xl2tpd ]
	then
		mkdir /var/run/xl2tpd
	fi
	daemon $SERVICE
	RETVAL=$?
	echo 
	if [ $RETVAL -eq 0 ];then
	   touch /var/lock/subsys/$SERVICE
	else
	   exit 7;
	fi
  return 0;
}

stop() {
	echo -n "Stopping $SERVICE: "
	killproc $SERVICE
	RETVAL=$?
	if [ $RETVAL -eq 0 ]; then
	   rm -f /var/run/xl2tpd/$SERVICE
	   rm -f /var/lock/subsys/$SERVICE
	fi
	echo
	return $RETVAL
}

restart() {
	stop
	start
}

# See how we were called.
case "$1" in
  start)
	start
	;;
  stop)
	stop
	;;
  status)
	status $SERVICE
	RETVAL=$?
	;;
  restart|reload)
	restart
	;;
  condrestart)
	[ -f /var/lock/subsys/$SERVICE ] && restart || :
	;;
  *)
	echo "Usage: $SERVICE {start|stop|status|restart|reload|condrestart}"
	exit 1
esac
