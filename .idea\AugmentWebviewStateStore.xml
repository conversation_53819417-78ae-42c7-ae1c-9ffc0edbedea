<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;6b5bbbbe-9050-43cf-b48c-02587681e137&quot;,&quot;conversations&quot;:{&quot;6b5bbbbe-9050-43cf-b48c-02587681e137&quot;:{&quot;id&quot;:&quot;6b5bbbbe-9050-43cf-b48c-02587681e137&quot;,&quot;createdAtIso&quot;:&quot;2025-06-30T03:42:11.836Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-30T03:54:48.526Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;02cf2507-efc8-4efe-bd70-1f2f67c66166&quot;,&quot;request_message&quot;:&quot;系统是 rockly linux 8.6 把ttttt/vpn.sh中的使用的vpnsetup_centos.sh改成本地调用 同时吧yum请求 改成dnf&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;系统是 rockly linux 8.6 把ttttt/vpn.sh中的使用的vpnsetup_centos.sh改成本地调用 同时吧yum请求 改成dnf&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;系统是 rockly linux 8.6 把ttttt/vpn.sh中的使用的vpnsetup_centos.sh改成本地调用 同时吧yum请求 改成dnf&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;E:/CursorWork/xl2tpd&quot;,&quot;folderRoot&quot;:&quot;E:/CursorWork/xl2tpd&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-30T03:43:59.042Z&quot;,&quot;response_text&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-702ea2f1-e63f-4e76-82ba-754d665b6998&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-4dadce27-8f8b-479f-8c29-5606ce8b7c07&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;draftActiveContextIds&quot;:[&quot;E:/CursorWork/xl2tpd/ttttt/vpn.sh&quot;,&quot;E:/CursorWork/xl2tpdfalsefalse&quot;,&quot;userGuidelines&quot;,&quot;agentMemories&quot;],&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;baselineTimestamp&quot;:0,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;b4c93232-2e6e-4632-be53-f411428fcf0e&quot;},&quot;__NEW_AGENT__&quot;:{&quot;id&quot;:&quot;__NEW_AGENT__&quot;,&quot;createdAtIso&quot;:&quot;2025-06-30T03:49:45.397Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-30T03:49:45.397Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;d4a7e2d9-de03-47c8-b2b2-8c28ed88247f&quot;,&quot;uuid&quot;:&quot;0b9ad0d3-6763-48d8-a0b0-4d071ac39c74&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1751255385397,&quot;seen_state&quot;:&quot;seen&quot;}],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;0b636030-f228-476f-9b93-cc86839a952f&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>