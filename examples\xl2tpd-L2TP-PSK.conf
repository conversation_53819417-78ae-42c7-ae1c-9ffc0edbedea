# /etc/ipsec.conf
version 2
	nat_traversal=yes
	# example assumes we using ***********/24 ourselves
	virtual_private=%v4:10.0.0.0/8,%v4:***********/16,%v4:**********/12,%v4:!***********/24.
	# Only the mast stack of Openswan supports SAref tracking
	protostack=mast
	#protostack=netkey

conn L2TP-PSK
	#
	# Configuration for one user with any type of IPsec/L2TP client
	# including the updated Windows 2000/XP (MS KB Q818043), but
	# excluding the non-updated Windows 2000/XP.
	#
	#
	# Use a Preshared Key. Disable Perfect Forward Secrecy.
	#
	authby=secret
	pfs=no
	#
	left=***************
	#
	leftprotoport=17/%any
	#
	# The remote user.
	#
	right=%any
	rightprotoport=17/%any
	rightsubnet=vhost:%priv,%no
	#
	# Change 'ignore' to 'add' to enable the configuration for this user.
	#
	auto=ignore
	keyingtries=3
	# Only the mast stack with Openswan supports SAref tracking with
	# overlapping IP address support
	overlapip=yes
	sareftrack=yes
