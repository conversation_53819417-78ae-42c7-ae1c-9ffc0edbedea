#!/usr/bin/make -f
# -*- makefile -*-
# Sample debian/rules that uses debhelper.
# This file was originally written by <PERSON> and <PERSON>.
# As a special exception, when this file is copied by dh-make into a
# dh-make output file, you may use that output file without restriction.
# This special exception was added by <PERSON> in version 0.37 of dh-make.

# Uncomment this to turn on verbose mode.
#export DH_VERBOSE=1

CFLAGS = -Wall -g

ifneq (,$(findstring noopt,$(DEB_BUILD_OPTIONS)))
	CFLAGS += -O0
else
	CFLAGS += -O2
endif

configure: configure-stamp
configure-stamp:
	dh_testdir
	touch configure-stamp

build: build-arch build-indep
build-arch: build-stamp
build-indep: build-stamp
build-stamp: configure-stamp 
	dh_testdir
	dh_auto_build -- CFLAGS=" -DDEBUG_PPPD -DTRUST_PPPD_TO_DIE -O2 -fno-builtin -Wall -DSANITY -DLINUX -I$(KERNELSRC)/include/ -DIP_ALLOCATION -DUSE_KERNEL $(shell dpkg-buildflags --get CFLAGS)" CPPFLAGS=" -DDEBUG_PPPD -DTRUST_PPPD_TO_DIE -O2 -fno-builtin -Wall -DSANITY -DLINUX -I$(KERNELSRC)/include/ -DIP_ALLOCATION $(shell dpkg-buildflags --get CPPFLAGS)" LDFLAGS=" $(shell dpkg-buildflags --get LDFLAGS)"
	touch $@

clean:
	dh_testdir
	dh_testroot
	rm -f build-stamp configure-stamp
	[ ! -f Makefile ] || $(MAKE) clean
	dh_clean 

install: build
	dh_testdir
	dh_testroot
	dh_prep
	dh_installdirs
	$(MAKE) PREFIX=/usr DESTDIR=$(CURDIR)/debian/xl2tpd install
	cp $(CURDIR)/doc/l2tpd.conf.sample $(CURDIR)/debian/xl2tpd/etc/xl2tpd/xl2tpd.conf
	cp $(CURDIR)/doc/l2tp-secrets.sample $(CURDIR)/debian/xl2tpd/etc/xl2tpd/l2tp-secrets

# Build architecture-independent files here.
binary-indep: build install
	# Nothing to do here

# Build architecture-dependent files here.
binary-arch: build install
	dh_testdir
	dh_testroot
	dh_installchangelogs CHANGES
	dh_installdocs
	dh_installexamples
#	dh_install
	dh_installinit
	dh_installman
	cp debian/lintian-overrides \
		debian/xl2tpd/usr/share/lintian/overrides/xl2tpd
	dh_link
	dh_strip
	dh_compress
	dh_fixperms
	chmod 600 $(CURDIR)/debian/xl2tpd/etc/xl2tpd/l2tp-secrets
	dh_installdeb
	dh_shlibdeps
	dh_gencontrol
	dh_md5sums
	dh_builddeb

binary: binary-indep binary-arch
.PHONY: build clean binary-indep binary-arch binary install configure
