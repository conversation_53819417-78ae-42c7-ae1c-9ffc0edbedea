要搭建L2TP/IPSec VPN + FreeRADIUS + MySQL环境，使用Daloradius管理，并通过Nginx的HTTP（IP+端口）访问，按以下步骤操作：

---

### **一、后台服务器配置（*************）**
#### 1. 安装基础环境
```bash
sudo dnf update -y
sudo dnf install -y epel-release
sudo dnf install -y git wget net-tools mariadb-server freeradius freeradius-mysql nginx 
sudo dnf install -y php-pear php-devel php php-fpm php-mysqlnd php-gd
sudo systemctl enable --now mariadb
```

#### 2. 配置MySQL
```bash
# 安全初始化MySQL
sudo mysql_secure_installation
这一步提示会创建root用户 并初始化用户密码 可自己输入 
用户root 密码 qW0uN6yQ1b

#### 3. 配置FreeRADIUS
```bash
# 导入FreeRADIUS表结构 
sudo su -
cd /etc/raddb/mods-config/sql/main/mysql/

// 这一步会创建 radius 用户 密码为 radpass
// 可修改 /etc/raddb/mods-config/sql/main/mysql/setup.sql  文件修改密码 大约15行
mysql -u root -p radius < setup.sql    
exit

# 启用SQL模块
sudo ln -s /etc/raddb/mods-available/sql /etc/raddb/mods-enabled/
sudo chgrp radiusd /etc/raddb/mods-enabled/sql
sudo chmod 640 /etc/raddb/mods-enabled/sql

# 编辑SQL配置
sudo vim /etc/raddb/mods-enabled/sql
```
修改以下内容：
参考文件 可直接用 /etc/raddb/mods-enabled/sql

#### 4. 安装配置Daloradius
```bash
cd /var/www/html
wget https://github.com/lirantal/daloradius/archive/refs/tags/1.3.zip
unzip 1.3.zip
mv daloradius-1.3 daloradius

sudo chown -R nginx:nginx daloradius/

# 导入Daloradius表结构
mysql -u root -p radius < /var/www/html/daloradius/contrib/db/fr2-mysql-daloradius-and-freeradius.sql

# 配置数据库连接
cp /var/www/html/daloradius/library/daloradius.conf.php.sample /var/www/html/daloradius/library/daloradius.conf.php
vim /var/www/html/daloradius/library/daloradius.conf.php
```
修改以下内容：
```php
$configValues['CONFIG_DB_HOST'] = '127.0.0.1';
$configValues['CONFIG_DB_USER'] = 'radius';
$configValues['CONFIG_DB_PASS'] = 'radpass';
$configValues['CONFIG_DB_NAME'] = 'radius';
```

#### 5. 配置Nginx
```bash
sudo vim /etc/nginx/conf.d/daloradius.conf
```
内容如下：
```nginx
server {
		listen       8080;
		server_name  _;
		root         /var/www/html/daloradius;
		index        index.php;

		# 新增重要参数
		client_max_body_size 10M;
		fastcgi_read_timeout 300;

		location / {
			try_files $uri $uri/ /index.php?$query_string;
		}

		location ~ \.php$ {
			try_files $uri =404;
			fastcgi_split_path_info ^(.+\.php)(/.+)$;
			fastcgi_pass   unix:/run/php-fpm/www.sock;
			fastcgi_index  index.php;
			fastcgi_param  SCRIPT_FILENAME $document_root$fastcgi_script_name;
			include        fastcgi_params;
			
			# 新增关键fastcgi参数
			fastcgi_param  PATH_INFO       $fastcgi_path_info;
			fastcgi_param  PHP_VALUE       "error_log=/var/log/nginx/php_errors.log";
		}

		# 错误日志单独存放
		error_log  /var/log/nginx/daloradius_error.log warn;
		access_log /var/log/nginx/daloradius_access.log main;
	}

```
启动服务：
```bash
sudo systemctl start php-fpm nginx
sudo systemctl enable php-fpm nginx
sudo firewall-cmd --add-port=8080/tcp --permanent
sudo firewall-cmd --reload
```

#### 6. 启动FreeRADIUS
```bash

# 重新执行启动 
sudo systemctl restart radiusd
sudo systemctl enable radiusd 
```

---

### **二、VPN服务器配置（*************）**
#### 1. 安装VPN组件
```bash
sudo dnf update -y
sudo dnf install -y epel-release
sudo dnf install -y libreswan xl2tpd ppp
```

#### 2. 配置IPSec（Libreswan）
```bash
sudo vim /etc/ipsec.d/ipsec.conf
```
内容如下：
```ini
conn L2TP-PSK
    authby=secret
    pfs=no
    auto=add
    keyingtries=3
    rekey=no
    ikelifetime=8h
    keylife=1h
    type=transport
    left=%defaultroute
    leftprotoport=17/1701
    right=%any
    rightprotoport=17/%any
```

```bash
sudo vim /etc/ipsec.d/ipsec.secrets
```
添加：
```
%any : PSK "TestL2#tp"  # 自定义预共享密钥
```

#### 3. 配置L2TP（xl2tpd）
```bash
sudo vim /etc/xl2tpd/xl2tpd.conf
```
内容如下：
```ini
[global]
ipsec saref = yes
listen-addr = *************
debug = yes
logfile = /var/log/xl2tpd/xl2tpd.log
```

```bash
sudo vim /etc/ppp/options.xl2tpd
```
内容如下：
```ini
require-mschap-v2
ms-dns *******
ms-dns *******
proxyarp
asyncmap 0
auth
crtscts
lock
hide-password
modem
name l2tpd
mtu 1200
mru 1200
nodefaultroute
debug
logfile /var/log/xl2tpd.log
plugin radius.so
radius-config-file /etc/radiusclient/radiusclient.conf
```

#### 4. 配置FreeRADIUS客户端
```bash
sudo dnf install -y freeradius-client
sudo vim /etc/radiusclient/radiusclient.conf
```
修改：
```ini
authserver *************
acctserver *************
secret TestL2#tp  # 与后台服务器clients.conf一致
```

```bash
sudo vim /etc/radiusclient/servers
```
添加：
```
************* TestL2#tp
```

#### 5. 配置后台服务器的RADIUS客户端
在**后台服务器**执行 *************：
```bash
sudo vim /etc/raddb/clients.conf
```
添加：
```ini
client vpn-server {
    ipaddr = *************
    secret = TestL2#tp  # 与VPN服务器配置一致
}
sudo systemctl restart radiusd
```

#### 6. 启动VPN服务--*************
```bash
sudo systemctl restart xl2tpd
#l2tpd----------------报错执行 start
# 1. 查看可用内核版本
sudo yum list kernel
# 2. 安装标准内核（例如）
sudo yum install kernel-4.18.0-477.10.1.el8_8
# 3. 设置为默认内核
sudo grubby --set-default /boot/vmlinuz-4.18.0-477.10.1.el8_8.x86_64
# 4. 重启系统
sudo reboot
#l2tpd----------------报错执行 end

sudo systemctl restart ipsec
#     报错则执行
# 检查是否有隐藏字符（如 Windows 换行符）
sudo cat -A /etc/ipsec.d/ipsec.conf
# 如果有 ^M 字符，清理文件
sudo sed -i 's/\r//g' /etc/ipsec.d/ipsec.conf
sudo sed -i 's/\r//g' /etc/ipsec.d/ipsec.secrets 

sudo systemctl restart ipsec 
sudo systemctl enable ipsec xl2tpd
```

#### 7. 防火墙与内核参数 服务端与客户端都执行
```bash
sudo systemctl start firewalld
sudo systemctl enable firewalld
sudo firewall-cmd --permanent --add-rich-rule='rule protocol value="esp" accept'
sudo firewall-cmd --permanent --add-rich-rule='rule protocol value="ah" accept'
sudo firewall-cmd --permanent --add-service=ipsec

sudo firewall-cmd --permanent --add-port=1812/udp --add-port=1813/udp
sudo firewall-cmd --permanent --add-port=1701/udp
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --permanent --add-port=500/udp
sudo firewall-cmd --permanent --add-port=4500/udp
sudo firewall-cmd --reload
sudo echo "net.ipv4.ip_forward=1" >> /etc/sysctl.conf
sudo sysctl -p
sudo firewall-cmd --list-ports
```

---

### **三、测试与使用**
1. **访问Daloradius管理界面**  
   URL: `http://*************:8080`  
   默认账号: `administrator` / `radius`

2. **添加VPN用户**
    - 登录Daloradius → "Users" → "Create User"
    - 设置用户名/密码，Group选择默认
- echo "User-Name=test001,User-Password=test001" | radclient -x ************* auth TestL2#tp

3. **客户端连接VPN**
    - 类型: L2TP/IPSec
    - 服务器IP: `*************`
    - IPSec预共享密钥: `TestL2#tp` (VPN服务器配置的密钥)
    - 账号: Daloradius中添加的用户

---

### **四、故障排查**
- **RADIUS日志**: `tail -f /var/log/radiusd/radius.log` (后台服务器)
- **VPN日志**: `tail -f /var/log/xl2tpd.log` (VPN服务器)
- **IPSec状态**: `sudo ipsec status`

通过以上步骤，您已成功搭建了基于L2TP/IPSec、FreeRADIUS+MySQL认证的VPN系统，并通过HTTP管理界面管理用户。


qW0uN6yQ1b