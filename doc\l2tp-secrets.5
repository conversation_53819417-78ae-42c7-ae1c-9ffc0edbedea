.TH "l2tp-secrets" "5" "" "<PERSON>" ""
.SH "NAME"
l2tp-secrets \- L2TPD secrets file
.SH "DESCRIPTION"
The l2tp-secrets file contains challenge-response authentication
information for xl2tpd, the implementation of l2tp protocol.  The format
of the file is derived from the pap and chap secrets file format used in
pppd.

The secrets file is composed of zero or more lines with 3 fields each.
Each line represents an authentication secret.  The 3 fields represent
our hostname, the remote hostname and the secret used in the
authentication process.

The first field is for our hostname, a "*" may be used as a wildcard.

The second field is for the remote system's hostname.  Again, a "*" may
be used as a wildcard.

The third field is the secret used if the previous two fields match the
hostnames of the systems involved.  The secret should, ideally, be at 16
characters long (the length of an MD5 digest output), and should
probably be longer to ensure sufficient security.  There is no minimum
length requirement, however.

.SH "FILES"

\fB\fR/etc/xl2tpd/xl2tpd.conf \fB\fR/etc/xl2tpd/l2tp\-secrets 
\fB\fR/var/run/xl2tpd/l2tp\-control
.SH "BUGS"

Please address bugs and <NAME_EMAIL>
.SH "SEE ALSO"

\fB\fRxl2tpd(8)
\fB\fRxl2tpd.conf(5)
.SH "AUTHORS"

Michael Richardson <<EMAIL>>
Paul Wouters <<EMAIL>>

Patched contributed by:
  Jacco de Leeuw <<EMAIL>>
  Cedric Schieli <<EMAIL>>

Previous development was hosted at sourceforge
(http://www.sourceforge.net/projects/l2tpd) by:
.P
Scott Balmos <<EMAIL>>
.br
David Stipp <<EMAIL>>
.br
Jeff McAdams <<EMAIL>>


Based off of l2tpd version 0.60
.br
Copyright (C)1998 Adtran, Inc.
.br
Mark Spencer <<EMAIL>>
