* Xelerance has forked l2tpd into xl2tpd.

* <PERSON> <<EMAIL>>, for adding IPsec SAref tracking
* <PERSON> <<EMAIL>>, for packaging, debugging and support.

* <PERSON><PERSON> <<EMAIL>> for various packaging and initscript patches
* <PERSON><PERSON>ki & <PERSON><PERSON> for SAref related patches.

Thanks to <PERSON><PERSON><PERSON> for his maintenance of the 0.69 version of l2tpd.

Original credits follow.

<PERSON> was the primary author of this work.  He would like to thank the
following people for their contributions:

* <PERSON>, Adtran, for supporting the creation of this free software

* <PERSON>, RedHat, for architectural suggestions and moral support :)

* <PERSON>, Ad<PERSON>n, for helping me get started and for providing me
  with the Adtran LAC for initial testing

* Ashish Lal, Midnight Networks, for thorughly evaluating compliance of l2tpd
  to the published l2tp specification

* <PERSON>, Deltacom, for loaning me a Cisco 3000 router for
  interoperability testing

* <PERSON>, Ad<PERSON><PERSON>, for initially pointing me in the direction of
  doing l2tp support for Linux

* <PERSON>, Cisco Systems, for helping answer a variety of questions
  (particularly relating to authentication) and for aiding with
  interoperability testing with Cisco l2tp software.

The MD5 code was written by <PERSON>, and is without copyright (public
domain).

This project was forked January 12, 2001 by <PERSON> and <PERSON> due
to the apparent inactivity of the project.

We also would like to thank the following people who helped us after the fork:

* Jeff McAdams, IgLou Internet Services, for being our own Alan Cox clone.

* Huiban Yoann, Siemens, for some scaleability improvements.

* Jens Zerbst, for initial implementation of a rudimentary Outgoing Call
  Request system

* Everyone out there who have submitted an uncountable amount of little
  bug fixes. Thanks all!!!

