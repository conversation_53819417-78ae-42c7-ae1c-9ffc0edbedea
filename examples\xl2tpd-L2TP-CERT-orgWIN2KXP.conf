conn L2TP-CERT-orgWIN2KXP
	#
	# Configuration for one user with the non-updated Windows 2000/XP.
	#
	#
	# Use a certificate. Disable Perfect Forward Secrecy.
	#
	authby=rsasig
	pfs=no
	#
	left=***************
	leftrsasigkey=%cert
	leftcert=/etc/ipsec.d/ssl/localCERT.pem
	#
	# Required for original (non-updated) Windows 2000/XP clients.
	leftprotoport=17/0
	#
	# The remote user.
	#
	right=%any
	rightrsasigkey=%cert
	rightcert=/etc/ipsec.d/ssl/userCERT.pem
	rightprotoport=17/1701
	#
	# Change 'ignore' to 'add' to enable the configuration for this user.
	#
	auto=ignore
	keyingtries=3
