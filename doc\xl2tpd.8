.TH xl2tpd 8 "Sep 2020"

.SH NAME
xl2tpd \- Layer 2 Tunnelling Protocol Daemon.

.SH DESCRIPTION
xl2tpd is an implementation of the Layer 2 Tunneling Protocol (RFC 2661).
L2TP allows to tunnel PPP over UDP. Some ISPs use L2TP to tunnel user sessions
from dial-in servers (modem banks, ADSL DSLAMs) to back-end PPP servers.
Another important application is Virtual Private Networks (VPN) where the
IPsec protocol is used to secure the L2TP connection (L2TP/IPsec, RFC 3193).

xl2tpd works by opening a pseudo-tty for communicating with pppd.
It runs completely in userspace but supports kernel mode L2TP.

xl2tpd supports IPsec SA Reference tracking to enable overlapping internak
NAT'ed IP's by different clients (eg all clients connecting from their
linksys internal IP *************) as well as multiple clients behind
the same NAT router.

This implementation is based on L2TPd 0.61 from http://www.marko.net/l2tp
and patches collected by Jacco de Leeuw at
http://www.jacco2.dds.nl/networking/openswan-l2tp.html.

.SH OPTIONS
.TP 
.B -D
This option prevents xl2tpd from detaching from the terminal and daemonizing.

.TP 
.B -l
This option tells xl2tpd to use syslog for logging even when \fB\-D\fR
was specified.

.TP
.B -c <config_file>
Set an alternate config file.
Fallback configuration file is /etc/l2tpd/l2tpd.conf.

.TP 
.B -s <secret_file>
Tells xl2tpd to use an alternate "secrets" file.

.TP 
.B -p <pid_file>
Set an alternate pid file.
Default is /var/run/xl2tpd/xl2tpd.pid.

.TP 
.B -C <control_file>
Set an alternate control file.


.SH FILES
.IP /etc/xl2tpd/xl2tpd.conf
Configuration file of xl2tpd, used by default.

.IP /etc/xl2tpd/l2tp-secrets
Secrets file, used by default.

.IP /var/run/xl2tpd/l2tp\-control
Control file, used by default.

.SH BUGS
Please use the github project page
https://github.com/xelerance/xl2tpd
to send bugreports, issues and any other feedback.

.SH SEE ALSO
xl2tpd.conf(5),
xl2tpd-control(8),
pppd(8)

.SH COPYLEFT
This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program (see the file LICENSE); if not, see
https://www.gnu.org/licenses/, or contact Free Software Foundation, Inc.,
51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.


.SH CONTRIBUTORS
Alexander Dorokhov <<EMAIL>>
.br
Alexander Naumov <<EMAIL>>


.SH AUTHORS
Forked from l2tpd by Xelerance: https://github.com/xelerance/xl2tpd

Michael Richardson <<EMAIL>>
.br
Paul Wouters <<EMAIL>>
.br
Samir Hussain <<EMAIL>>


Previous development was hosted at sourceforge
(http://www.sourceforge.net/projects/l2tpd) by:
.P
Scott Balmos <<EMAIL>>
.br
David Stipp <<EMAIL>>
.br
Jeff McAdams <<EMAIL>>

Based off of l2tpd version 0.61.
Many thanks to Jacco de Leeuw <<EMAIL>> for maintaining l2tpd.
.br
Copyright (C)1998 Adtran, Inc.
.br
Mark Spencer <<EMAIL>>
