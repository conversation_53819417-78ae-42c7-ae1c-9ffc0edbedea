#! /bin/sh
# Copyright (c) 1995-2003 <PERSON> <<EMAIL>>
# Tronicplanet Datendienst GmbH, Simbach am INN, Germany.
# All rights reserved.
# Copyright (c) 2006-2008 <PERSON> <<EMAIL>>
# Xeleracne Corporation
#
# /etc/init.d/xl2tpd
#
#   and its symbolic link
#
# /usr/sbin/rcxl2tpd
#
# LSB compliant service control script; see http://www.linuxbase.org/spec/
# 
# System startup script for L2TP daemon xl2tpd
#
### BEGIN INIT INFO
# Provides: xl2tpd
# Required-Start: $syslog $remote_fs
# Required-Stop:  $syslog $remote_fs
# Default-Start:  3 5
# Default-Stop:   0 1 2 6
# Short-Description: Start xl2tpd (to provide L2TP VPN's)
# Description:    Start xl2tpd to provide L2TP VPN tunnels
#	normally used with IPsec (openswan)
### END INIT INFO
# 
# Note on Required-Start: It does specify the init script ordering,
# not real dependencies. Depencies have to be handled by admin
# resp. the configuration tools (s)he uses.

# Source SuSE config (if still necessary, most info has been moved)
test -r /etc/rc.config && . /etc/rc.config

# Check for missing binaries (stale symlinks should not happen)
XL2TPD_BIN=/usr/sbin/xl2tpd
test -x $YPBIND_BIN || { echo "$YPBIND_BIN not installed";
        if [ "$1" = "stop" ]; then exit 0; else exit 5; fi; }

# Check for existence of needed config file and read it
#XL2TPD_CONFIG=/etc/sysconfig/xl2tpd
#test -r $YPBIND_CONFIG || { echo "$YPBIND_CONFIG not existing";
#        if [ "$1" = "stop" ]; then exit 0; else exit 6; fi; }
#. $XL2TPD_CONFIG

# Shell functions sourced from /etc/rc.status:
#      rc_check         check and set local and overall rc status
#      rc_status        check and set local and overall rc status
#      rc_status -v     ditto but be verbose in local rc status
#      rc_status -v -r  ditto and clear the local rc status
#      rc_failed        set local and overall rc status to failed
#      rc_failed <num>  set local and overall rc status to <num><num>
#      rc_reset         clear local rc status (overall remains)
#      rc_exit          exit appropriate to overall rc status
#      rc_active	checks whether a service is activated by symlinks
. /etc/rc.status

# First reset status of this service
rc_reset

# Return values acc. to LSB for all commands but status:
# 0 - success
# 1 - generic or unspecified error
# 2 - invalid or excess argument(s)
# 3 - unimplemented feature (e.g. "reload")
# 4 - insufficient privilege
# 5 - program is not installed
# 6 - program is not configured
# 7 - program is not running
# 
# Note that starting an already running service, stopping
# or restarting a not-running service as well as the restart
# with force-reload (in case signalling is not supported) are
# considered a success.

case "$1" in
    start)
	echo -n "Starting xl2tpd"
	## Start daemon with startproc(8). If this fails
	## the echo return value is set appropriate.

	# NOTE: startproc returns 0, even if service is 
	# already running to match LSB spec.
	startproc $XL2TPD_BIN >/dev/null 2>&1

	# Remember status and be verbose
	rc_status -v
	;;
    stop)
	echo -n "Shutting down xl2tpd"
	## Stop daemon with killproc(8) and if this fails
	## set echo the echo return value.

	killproc -TERM $XL2TPD_BIN
	rm -f /var/run/xl2tpd/xl2tpd.pid
	# Remember status and be verbose
	rc_status -v
	;;
    try-restart|condrestart)
        ## Do a restart only if the service was active before.
        ## Note: try-restart is now part of LSB (as of 1.9).
        ## RH has a similar command named condrestart.
        if test "$1" = "condrestart"; then
                echo "${attn} Use try-restart ${done}(LSB)${attn} rather than condrestart ${warn}(RH)${norm}"
        fi
        $0 status
        if test $? = 0; then
                $0 restart
        else
                rc_reset        # Not running is not a failure.
        fi
        rc_status
        ;;
    restart)
        $0 stop
        sleep 1
        $0 start
        rc_status
        ;;
    force-reload)
	## Signal the daemon to reload its config. Most daemons
	## do this on signal 1 (SIGHUP).
	## If it does not support it, restart.
	echo -n "Reload service xl2tpd"
	## if it supports it:
	killproc -HUP $XL2TPD_BIN
	#touch /var/run/xl2tpd/xl2tpd.pid
	rc_status -v
	## Otherwise:
	#$0 stop  &&  $0 start
	#rc_status
	;;
    reload)
	## Like force-reload, but if daemon does not support
	## signalling, do nothing (!)
	# If it supports signalling:
	echo -n "Reload service xl2tpd"
	killproc -HUP $XL2TPD_BIN
	#touch /var/run/xl2tpd.pid
	rc_status -v
	## Otherwise if it does not support reload:
	#rc_failed 3
	#rc_status -v
	;;
    status)
	echo -n "Checking for service xl2tpd: "
	## Check status with checkproc(8), if process is running
	## checkproc will return with exit status 0.

	# Return value is slightly different for the status command:
	# 0 - service running
	# 1 - service dead, but /var/run/  pid  file exists
	# 2 - service dead, but /var/lock/ lock file exists
	# 3 - service not running

	# NOTE: checkproc returns LSB compliant status values.
	checkproc $XL2TPD_BIN
	rc_status -v
	;;
    probe)
	## Optional: Probe for the necessity of a reload,
	## print out the argument which is required for a reload.

	test /etc/xl2tpd/xl2tpd.conf -nt /var/run/xltpd/xl2tpd.pid && echo reload
	;;
    *)
	echo "Usage: $0 {start|stop|status|try-restart|restart|force-reload|reload|probe}"
	exit 1
	;;
esac
rc_exit
