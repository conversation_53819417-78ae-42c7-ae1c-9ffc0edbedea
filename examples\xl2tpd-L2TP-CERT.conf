# /etc/ipsec.conf
version 2

config setup
	nat_traversal=yes
	# example assumes we using ***********/24 ourselves
	virtual_private=%v4:10.0.0.0/8,%v4:***********/16,%v4:**********/12,%v4:!***********/24.
	# Only the mast stack of Openswan supports SAref tracking
	protostack=mast
	#protostack=netkey

conn L2TP-CERT
	#
	# Configuration for one user with any type of IPsec/L2TP client
	# including the updated Windows 2000/XP (MS KB Q818043), but
	# excluding the non-updated Windows 2000/XP.
	#
	#
	# Use a certificate. Disable Perfect Forward Secrecy.
	#
	authby=rsasig
	pfs=no
	#
	left=***************
	leftrsasigkey=%cert
	leftcert=/etc/ipsec.d/ssl/localCERT.pem
	#
	leftprotoport=17/1701
	#
	# The remote user.
	#
	right=%any
	rightrsasigkey=%cert
	rightcert=/etc/ipsec.d/ssl/userCERT.pem
	rightsubnet=vhost:%priv,%no
	rightprotoport=17/%any
	#
	# Change 'ignore' to 'add' to enable the configuration for this user.
	#
	auto=ignore
	keyingtries=3
	# Only the mast stack with Openswan supports SAref tracking with
	# overlapping IP address support
	overlapip=yes
	sareftrack=yes
